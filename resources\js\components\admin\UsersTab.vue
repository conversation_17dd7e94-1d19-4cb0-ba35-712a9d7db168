<script setup lang="ts">
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Icon from '@/components/Icon.vue'

const users = ref<any[]>([])
const loading = ref(false)
const error = ref('')

onMounted(async () => {
  loading.value = true
  try {
    const res = await axios.get('/api/users')
    users.value = res.data.data || res.data // Tùy API trả về
  } catch (e: any) {
    error.value = 'Không thể tải danh sách người dùng.'
  } finally {
    loading.value = false
  }
})

const getStatusClass = (status: string) => {
  switch (status) {
    case 'Đang hoạt động':
      return 'bg-green-100 text-green-800'
    case 'Ngừng hoạt động':
      return 'bg-yellow-100 text-yellow-800'
    case 'Bị khóa':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold">
        Quản Lý Người Dùng
      </h2>
      <Button>
        <Icon name="Plus" class="h-4 w-4 mr-2" />
        Thêm người dùng
      </Button>
    </div>
    <div class="flex gap-4 mb-4">
      <Input placeholder="Tìm kiếm người dùng..." class="max-w-xs" />
      <select class="border rounded px-2 py-1">
        <option>Tất cả vai trò</option>
      </select>
      <select class="border rounded px-2 py-1">
        <option>Tất cả trạng thái</option>
      </select>
    </div>
    <div v-if="loading" class="p-4 text-center text-gray-500">Đang tải...</div>
    <div v-else-if="error" class="p-4 text-center text-red-500">{{ error }}</div>
    <div v-else class="border rounded-md">
      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              Tên người dùng
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              Email
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              Vai trò
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              Đơn vị
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              Trạng thái
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              Ngày tạo
            </th>
            <th class="p-3 w-[50px]"></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(user, index) in users" :key="user.id || index" class="border-b">
            <td class="p-3">
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-full bg-muted flex items-center justify-center font-semibold">
                  {{ user.name?.charAt(0) }}
                </div>
                <div>
                  <div class="font-medium">
                    {{ user.name }}
                  </div>
                  <div class="text-sm text-muted-foreground">
                    {{ user.username }}
                  </div>
                </div>
              </div>
            </td>
            <td class="p-3 text-sm">
              {{ user.email }}
            </td>
            <td class="p-3 text-sm">
              {{ user.role?.name || user.role || '' }}
            </td>
            <td class="p-3 text-sm">
              {{ user.unit || '' }}
            </td>
            <td class="p-3">
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getStatusClass(user.status)"
              >
                {{ user.status || 'Đang hoạt động' }}
              </span>
            </td>
            <td class="p-3 text-sm">
              {{ user.created_at ? (new Date(user.created_at).toLocaleDateString('vi-VN')) : '' }}
            </td>
            <td class="p-3">
              <Button variant="ghost" size="icon">
                <Icon name="MoreHorizontal" class="h-4 w-4" />
              </Button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="flex justify-between items-center mt-4 text-sm text-muted-foreground">
      <span>Hiển thị {{ users.length ? `1 đến ${users.length}` : '0' }} của {{ users.length }} người dùng</span>
    </div>
  </div>
</template>
