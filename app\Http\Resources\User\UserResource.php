<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'username' => $this->username ?? null, // Nếu có trường username
            'avatar' => $this->avatar ?? null,
            'email_verified_at' => $this->email_verified_at?->toISOString(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            
            // Thông tin vai trò và quyền
            'roles' => $this->whenLoaded('roles', function () {
                return $this->roles->map(function ($role) {
                    return [
                        'id' => $role->id,
                        'name' => $role->name,
                        'display_name' => $role->display_name ?? $role->name,
                    ];
                });
            }),
            
            'permissions' => $this->whenLoaded('permissions', function () {
                return $this->permissions->map(function ($permission) {
                    return [
                        'id' => $permission->id,
                        'name' => $permission->name,
                        'display_name' => $permission->display_name ?? $permission->name,
                    ];
                });
            }),
            
            // Thông tin vai trò đơn giản cho hiển thị
            'role' => $this->whenLoaded('roles', function () {
                $firstRole = $this->roles->first();
                return $firstRole ? [
                    'id' => $firstRole->id,
                    'name' => $firstRole->display_name ?? $firstRole->name,
                ] : null;
            }),
            
            // Trạng thái người dùng
            'status' => $this->status ?? ($this->email_verified_at ? 'Đang hoạt động' : 'Chưa xác thực'),
            
            // Đơn vị (nếu có)
            'unit' => $this->unit ?? null,
            
            // Thông tin bổ sung
            'last_login_at' => $this->last_login_at?->toISOString(),
            'is_active' => $this->is_active ?? true,
        ];
    }
}
