<?php

use App\Http\Controllers\API\Taisan\CongdapController;
use App\Http\Controllers\API\Reports\B1AReportController;
use App\Http\Controllers\API\QuyetToanController;
use App\Http\Controllers\API\Reports\AssetStatsController;
use App\Http\Controllers\API\SpatialController;
use App\Http\Controllers\API\Taisan\AssetTemplateController;
use App\Http\Controllers\API\UserController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Quyết toán routes
Route::get('quyet-toan-options', [QuyetToanController::class, 'getOptions']);

/* Route::middleware('auth:sanctum')->prefix('taisan')->group(function () { */

// Tài sản routes
Route::prefix('taisan')->group(function () {
   Route::prefix('congdap')->group(function () {
      // Đặt các route cụ thể trước route resource để tránh xung đột
      Route::get('geometry', [CongdapController::class, 'geometry'])->name('congdap.geometry');
      Route::get('attributes', [CongdapController::class, 'attributes'])->name('congdap.attributes');
      Route::get('template', [AssetTemplateController::class, 'download'])->name('congdap.template');
      Route::post('import', [CongdapController::class, 'import'])->name('congdap.import');
      Route::get('{congdap}', [CongdapController::class, 'show'])->name('congdap.show');
      Route::put('{congdap}', [CongdapController::class, 'update'])->name('congdap.update');
      Route::delete('{congdap}', [CongdapController::class, 'destroy'])->name('congdap.destroy');
      // Đặt apiResource sau các route cụ thể
      Route::apiResource('', CongdapController::class);

   });
});

// Báo cáo routes
Route::prefix('reports')->group(function () {
   Route::get('b1a', [B1AReportController::class, 'index'])->name('reports.b1a');
   Route::get('b1a/export', [B1AReportController::class, 'export'])->name('reports.b1a.export');
});

// Route thống kê tài sản
Route::get('assets/stats', [AssetStatsController::class, 'getStats']);

// Spatial query routes
Route::prefix('spatial')->group(function () {
   Route::post('find-xa-by-coordinates', [SpatialController::class, 'findXaByCoordinates'])->name('spatial.find-xa');
   Route::get('xa-list', [SpatialController::class, 'getXaList'])->name('spatial.xa-list');
});

// User management routes
Route::prefix('users')->group(function () {
    // Đặt các route cụ thể trước route resource để tránh xung đột
    Route::get('{user}', [UserController::class, 'show'])->name('users.show');
    Route::put('{user}', [UserController::class, 'update'])->name('users.update');
    Route::delete('{user}', [UserController::class, 'destroy'])->name('users.destroy');
    // Đặt apiResource sau các route cụ thể
    Route::apiResource('', UserController::class);
});
