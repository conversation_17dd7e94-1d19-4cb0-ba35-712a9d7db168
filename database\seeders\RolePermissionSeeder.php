<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log; // Thêm Log facade để ghi token ra file log

class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        // 1. Tạo permission (giữ nguyên)
        $permissions = [
            ['name' => 'view_data', 'vi_name' => 'Xem dữ liệu'],
            ['name' => 'create_data', 'vi_name' => 'Tạo dữ liệu'],
            ['name' => 'edit_data', 'vi_name' => 'Sửa dữ liệu'],
            ['name' => 'delete_data', 'vi_name' => 'Xoá dữ liệu'],
            ['name' => 'manage_users', 'vi_name' => 'Quản lý người dùng'],
            ['name' => 'update_info', 'vi_name' => 'Cập nhật thông tin'],
            ['name' => 'generate_reports', 'vi_name' => 'Tạo báo cáo'],
            ['name' => 'calculate_wear', 'vi_name' => 'Tính toán hao mòn'],
        ];
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission['name']], ['vi_name' => $permission['vi_name']]);
        }

        // 2. Tạo role và gán permission (giữ nguyên)
        Role::firstOrCreate(['name' => 'admin'], ['vi_name' => 'Quản trị viên'])->givePermissionTo(Permission::all());
        Role::firstOrCreate(['name' => 'specialist_data'], ['vi_name' => 'Chuyên viên Dữ liệu'])->givePermissionTo(['view_data','create_data','edit_data','delete_data','update_info','calculate_wear']);
        Role::firstOrCreate(['name' => 'specialist_report'], ['vi_name' => 'Chuyên viên Báo cáo'])->givePermissionTo(['view_data','generate_reports','update_info','calculate_wear']);
        Role::firstOrCreate(['name' => 'specialist_user'], ['vi_name' => 'Chuyên viên Người dùng'])->givePermissionTo(['view_data','manage_users','update_info','calculate_wear']);
        Role::firstOrCreate(['name' => 'guest'], ['vi_name' => 'Khách'])->givePermissionTo('view_data');

        // 3. Tạo users và token
        $this->command->info('Creating users and tokens...'); // Thông báo trên console

        // --- User Admin ---
        $adminUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Nguyễn Văn A',
                'password' => Hash::make('123'),
                'id_xa' => '27595',
            ]
        );
        $adminUser->assignRole('admin');
        // Tạo token và log ra
        $adminToken = $adminUser->createToken('admin-seed-token')->plainTextToken;
        Log::info("Admin Token (<EMAIL>): " . $adminToken);
        $this->command->line("Admin Token (<EMAIL>): <fg=yellow>$adminToken</>"); // Hiển thị trên console

        // --- User Specialist ---
        $specialistUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Lê Hoàng Lương',
                'password' => Hash::make('123'),
                'id_xa' => null,
            ]
        );
        $specialistUser->assignRole('specialist_data', 'specialist_report', 'specialist_user');
        // Tạo token và log ra
        $specialistToken = $specialistUser->createToken('specialist-seed-token')->plainTextToken;
        Log::info("Specialist Token (<EMAIL>): " . $specialistToken);
        $this->command->line("Specialist Token (<EMAIL>): <fg=yellow>$specialistToken</>");

        // --- User Guest ---
        $guestUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Người dùng Khách',
                'password' => Hash::make('123'),
                'id_xa' => null,
            ]
        );
        $guestUser->assignRole('guest');
        // Tạo token và log ra
        $guestToken = $guestUser->createToken('guest-seed-token')->plainTextToken;
        Log::info("Guest Token (<EMAIL>): " . $guestToken);
        $this->command->line("Guest Token (<EMAIL>): <fg=yellow>$guestToken</>");

        $this->command->info('Users and tokens created successfully.');
    }
}
