<?php

namespace App\Services;

use App\Models\User;
use App\Exceptions\User\UserNotFoundException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Builder;

class UserService
{
    public function getList(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $cacheKey = $this->generateCacheKey('users_list', $filters, $perPage);

        // Kiểm tra xem cache driver có hỗ trợ tags không
        $driver = config('cache.default');
        $supportedDrivers = ['redis', 'memcached', 'array'];

        if (in_array($driver, $supportedDrivers)) {
            return Cache::tags(['users-list'])->remember($cacheKey, 300, function () use ($filters, $perPage) {
                $query = User::with(['roles', 'permissions']);

                $this->applyFilters($query, $filters);

                return $query->paginate($perPage);
            });
        } else {
            // Fallback: sử dụng cache thông thường với version key
            $versionKey = 'users_version';
            $version = Cache::get($versionKey, 0);
            $versionedCacheKey = "{$cacheKey}_v{$version}";

            return Cache::remember($versionedCacheKey, 300, function () use ($filters, $perPage) {
                $query = User::with(['roles', 'permissions']);

                $this->applyFilters($query, $filters);

                return $query->paginate($perPage);
            });
        }
    }

    public function getById(int $id): User
    {
        $cacheKey = "user_{$id}";

        // Kiểm tra xem cache driver có hỗ trợ tags không
        $driver = config('cache.default');
        $supportedDrivers = ['redis', 'memcached', 'array'];

        if (in_array($driver, $supportedDrivers)) {
            $user = Cache::tags(['users-list'])->remember($cacheKey, 300, function () use ($id) {
                return User::with(['roles', 'permissions'])->find($id);
            });
        } else {
            // Fallback: sử dụng cache thông thường với version key
            $versionKey = 'users_version';
            $version = Cache::get($versionKey, 0);
            $versionedCacheKey = "{$cacheKey}_v{$version}";

            $user = Cache::remember($versionedCacheKey, 300, function () use ($id) {
                return User::with(['roles', 'permissions'])->find($id);
            });
        }

        if (!$user) {
            throw new UserNotFoundException($id);
        }

        return $user;
    }

    public function create(array $data): User
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        // Extract roles and permissions
        $roles = $data['roles'] ?? [];
        $permissions = $data['permissions'] ?? [];
        unset($data['roles'], $data['permissions']);

        $user = User::create($data);

        // Assign roles and permissions
        if (!empty($roles)) {
            $user->assignRole($roles);
        }

        if (!empty($permissions)) {
            $user->givePermissionTo($permissions);
        }

        // Load relationships for response
        $user->load(['roles', 'permissions']);

        return $user;
    }

    public function update(int $id, array $data): User
    {
        $user = User::find($id);

        if (!$user) {
            throw new UserNotFoundException($id);
        }

        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } else {
            unset($data['password']);
        }

        // Extract roles and permissions
        $roles = $data['roles'] ?? null;
        $permissions = $data['permissions'] ?? null;
        unset($data['roles'], $data['permissions']);

        // Update user data
        $user->update($data);

        // Update roles if provided
        if ($roles !== null) {
            $user->syncRoles($roles);
        }

        // Update permissions if provided
        if ($permissions !== null) {
            $user->syncPermissions($permissions);
        }

        // Load relationships for response
        $user->load(['roles', 'permissions']);

        return $user;
    }

    public function delete(int $id): bool
    {
        $user = User::find($id);

        if (!$user) {
            throw new UserNotFoundException($id);
        }

        // Remove all roles and permissions before deleting
        $user->syncRoles([]);
        $user->syncPermissions([]);

        return $user->delete();
    }

    protected function applyFilters(Builder $query, array $filters): void
    {
        // Search filter
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'ILIKE', "%{$search}%")
                  ->orWhere('email', 'ILIKE', "%{$search}%");
            });
        }

        // Role filter
        if (!empty($filters['role'])) {
            $query->whereHas('roles', function ($q) use ($filters) {
                $q->where('name', $filters['role']);
            });
        }

        // Status filter
        if (!empty($filters['status'])) {
            switch ($filters['status']) {
                case 'active':
                    $query->whereNotNull('email_verified_at');
                    break;
                case 'inactive':
                    $query->whereNull('email_verified_at');
                    break;
            }
        }

        // Email verified filter
        if (isset($filters['email_verified'])) {
            if ($filters['email_verified']) {
                $query->whereNotNull('email_verified_at');
            } else {
                $query->whereNull('email_verified_at');
            }
        }

        // Date range filter
        if (!empty($filters['created_from'])) {
            $query->where('created_at', '>=', $filters['created_from']);
        }

        if (!empty($filters['created_to'])) {
            $query->where('created_at', '<=', $filters['created_to']);
        }
    }

    protected function generateCacheKey(string $prefix, array $filters, int $perPage): string
    {
        $filterString = md5(serialize($filters));
        return "{$prefix}_{$filterString}_{$perPage}";
    }
}
